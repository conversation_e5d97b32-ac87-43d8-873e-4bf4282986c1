CREATE TABLE Clients (
    Id varchar(50) NOT NULL,
    <PERSON>lientCode varchar(20) NOT NULL,
    FirstName varchar(200) NOT NULL,
    MiddleName varchar(200),
    LastName varchar(200) NOT NULL,
    DateOfBirth datetime2,
    <PERSON> varchar(100) NOT NULL,
    <PERSON><PERSON><PERSON> varchar(100),
    <PERSON><PERSON> varchar(100),
    <PERSON> varchar(20),
    CreatedDate datetime2,
    LastUpdatedDate datetime2,
    ClientType nvarchar(100),
    Dom<PERSON>le nvarchar(20),
    BseStarUcc varchar(50),
    Title nvarchar(50),
    UserId varchar(50),
    UserName varchar(100),
    PctTotalDebtConsent int,
    PctTotalEquityConsent int,
    PctTotalLimitConsent int,
    CKYCNo varchar(50),
    FullName varchar(400),
    IsAccreditedInvestor bit,
    NameAsPerPan varchar(200),
    ClientStatus varchar(50),
    PRIMARY KEY (Id)
);

CREATE VIEW vw_query_ClientWithDetails
AS
SELECT
    c.Id,
    c.ClientCode,
    c.First<PERSON>ame,
    c.<PERSON>,
    c.DateOfBirth,
    c.Pan,
    c.Email AS email,
    c.Phone,
    c.ClientType,
    c.Domicile,
    c.FullName,
    c.ClientStatus,
    a.AddressLine1,
    a.AddressLine2,
    a.City,
    a.State,
    a.Country,
    a.PinCode,
    a.Email AS EmailPrimary,
    a.Phone AS MobilePrimary
FROM Clients c
LEFT JOIN Addresses a ON c.Id = a.ClientId;