use alpha_report_builder::db::lake::DeltaLakeStruct;
use alpha_report_builder::db::mssql::connection::pool::Manager;
use alpha_report_builder::{db::error::DatabaseError, state::AppState};
use arrow::array::{self};
use arrow::datatypes::{DataType, Field, Schema};
use arrow::record_batch::RecordBatch;
use chrono::NaiveDate;
use chrono::{DateTime, Duration, NaiveDateTime};
use deadpool::managed::Object;
use futures::TryStreamExt;
use std::sync::Arc;
use tiberius::{ColumnData, QueryItem};

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();
    let mut state = AppState::new().await;
    let schema = get_arrow_schema(&mut state.pool).await.unwrap();
    get_all(&mut state.pool, schema.unwrap()).await.unwrap();
}

pub async fn get_arrow_schema(
    conn: &mut Object<Manager>,
) -> Result<Option<Arc<Schema>>, DatabaseError> {
    let mut stream = conn
        .query(
            r#"SELECT
                    c.COLUMN_NAME,
                    c.DATA_TYPE,
                    c.CHARACTER_MAXIMUM_LENGTH,
                    c.IS_NULLABLE
                FROM INFORMATION_SCHEMA.COLUMNS c
                WHERE c.TABLE_SCHEMA = 'dbo'
                  AND c.TABLE_NAME = 'vw_query_ClientWithDetails'
                ORDER BY c.ORDINAL_POSITION;"#,
            &[],
        )
        .await
        .expect("Failed to execute query");
    let mut schema: Vec<Field> = Vec::new();
    while let Some(item) = stream.try_next().await? {
        match item {
            QueryItem::Metadata(_meta) => {}
            QueryItem::Row(row) => {
                let col_name: &str = row.get(0).unwrap(); // COLUMN_NAME
                let data_type: &str = row.get(1).unwrap(); // DATA_TYPE
                let char_max_len: Option<i32> = row.get(2); // CHARACTER_MAXIMUM_LENGTH
                let is_nullable: &str = row.get(3).unwrap(); // IS_NULLABLE

                // You can build your Arrow Field here
                let arrow_type = sql_type_to_arrow(data_type, char_max_len);
                let nullable = is_nullable == "YES";

                let field = Field::new(col_name, arrow_type, nullable);
                // push to your schema vector
                schema.push(field);
            }
        }
    }
    Ok(Some(Arc::new(Schema::new(schema))))
}

pub async fn get_all(
    conn: &mut Object<Manager>,
    arrow_schema: Arc<Schema>,
) -> Result<(), DatabaseError> {
    let mut stream = conn
        .query("SELECT TOP 1 * FROM vw_query_ClientWithDetails", &[])
        .await
        .expect("Failed to execute query");

    let mut builders: Option<Vec<Box<dyn array::ArrayBuilder>>> = Some(
        arrow_schema
            .as_ref()
            .fields()
            .iter()
            .map(|f| builder_from_datatype(f.data_type()))
            .collect(),
    );

    while let Some(item) = stream.try_next().await? {
        match item {
            QueryItem::Metadata(_meta) => {}

            QueryItem::Row(row) => {
                if let Some(ref mut blds) = builders {
                    // Step 3: Append values into builders
                    for (i, col_data) in row.into_iter().enumerate() {
                        let dtype = arrow_schema.field(i).data_type();
                        append_to_builder(&mut blds[i], col_data, dtype);
                    }
                }
            }
        }
    }

    // Step 4: Flush into RecordBatch when done
    if let (schema, Some(blds)) = (arrow_schema, builders) {
        let arrays: Vec<array::ArrayRef> = blds
            .into_iter()
            .map(|mut b| b.finish())
            .enumerate()
            .map(|(i, arr)| {
                let expected = schema.field(i).data_type();
                if expected != arr.data_type() {
                    arrow::compute::cast(&arr, expected).unwrap()
                } else {
                    arr
                }
            })
            .collect();
        let batch = RecordBatch::try_new(schema.clone(), arrays).expect("failed to generate batch");

        // DeltaLakeStruct::upsert_record_batch(vec![batch], "s3://report-builder/clients_views", "Id")
        //     .await
        //     .expect("Failed to update batch");
        // println!("Updated batch");

        DeltaLakeStruct::insert_record_batch(vec![batch], "s3://report-builder/clients_views")
            .await
            .expect("Failed to insert batch");
    }
    Ok(())
}

use arrow::datatypes::TimeUnit;

fn builder_from_datatype(dt: &DataType) -> Box<dyn array::ArrayBuilder> {
    match dt {
        DataType::Int8 => Box::new(array::Int8Builder::new()),
        DataType::Int16 => Box::new(array::Int16Builder::new()),
        DataType::Int32 => Box::new(array::Int32Builder::new()),
        DataType::Int64 => Box::new(array::Int64Builder::new()),

        DataType::UInt8 => Box::new(array::UInt8Builder::new()),
        DataType::UInt16 => Box::new(array::UInt16Builder::new()),
        DataType::UInt32 => Box::new(array::UInt32Builder::new()),
        DataType::UInt64 => Box::new(array::UInt64Builder::new()),

        DataType::Float32 => Box::new(array::Float32Builder::new()),
        DataType::Float64 => Box::new(array::Float64Builder::new()),

        DataType::Boolean => Box::new(array::BooleanBuilder::new()),

        DataType::Utf8 | DataType::LargeUtf8 => Box::new(array::StringBuilder::new()),

        DataType::Binary | DataType::LargeBinary => Box::new(array::BinaryBuilder::new()),

        DataType::Date32 => Box::new(array::Date32Builder::new()),
        DataType::Date64 => Box::new(array::Date64Builder::new()),

        DataType::Timestamp(TimeUnit::Second, tz) => {
            Box::new(array::TimestampSecondBuilder::new().with_timezone_opt(tz.clone()))
        }
        DataType::Timestamp(TimeUnit::Millisecond, tz) => {
            Box::new(array::TimestampMillisecondBuilder::new().with_timezone_opt(tz.clone()))
        }
        DataType::Timestamp(TimeUnit::Microsecond, tz) => {
            Box::new(array::TimestampMicrosecondBuilder::new().with_timezone_opt(tz.clone()))
        }
        DataType::Timestamp(TimeUnit::Nanosecond, tz) => {
            Box::new(array::TimestampNanosecondBuilder::new().with_timezone_opt(tz.clone()))
        }

        // fallback for unsupported types (store as Utf8 for now)
        _ => Box::new(array::StringBuilder::new()),
    }
}

fn decode_date(d: &tiberius::time::Date) -> NaiveDate {
    let base = NaiveDate::from_ymd_opt(1, 1, 1).unwrap();
    base + Duration::days(d.days() as i64)
}

fn decode_time(t: &tiberius::time::Time) -> String {
    // increments depend on scale (10^scale increments per second)
    let nanos = (t.increments() as i64) * 10_i64.pow(9 - t.scale() as u32);
    let secs = nanos / 1_000_000_000;
    let nsecs = (nanos % 1_000_000_000) as u32;

    let dt = DateTime::from_timestamp(secs, nsecs)
        .unwrap_or_else(|| DateTime::from_timestamp(0, 0).unwrap());
    dt.time().format("%H:%M:%S%.f").to_string()
}

fn decode_datetime2(dt: &tiberius::time::DateTime2) -> NaiveDateTime {
    let date = decode_date(&dt.date());
    let time = decode_time(&dt.time());
    NaiveDateTime::parse_from_str(
        &format!("{} {}", date.format("%Y-%m-%d"), time),
        "%Y-%m-%d %H:%M:%S%.f",
    )
    .unwrap()
}

fn append_to_builder(b: &mut dyn array::ArrayBuilder, col_data: ColumnData, dtype: &DataType) {
    match b {
        b if b.as_any().is::<array::Int32Builder>() => {
            let b = b
                .as_any_mut()
                .downcast_mut::<array::Int32Builder>()
                .unwrap();
            if let ColumnData::I32(Some(v)) = col_data {
                b.append_value(v);
            } else {
                b.append_null();
            }
        }
        b if b.as_any().is::<array::StringBuilder>() => {
            let b = b
                .as_any_mut()
                .downcast_mut::<array::StringBuilder>()
                .unwrap();
            match col_data {
                ColumnData::String(Some(s)) => b.append_value(s),
                ColumnData::DateTime2(Some(dt)) => {
                    let decoded = decode_datetime2(&dt);
                    b.append_value(decoded.to_string());
                }
                _ => b.append_null(),
            }
        }
        // add other types as needed
        b if b.as_any().is::<array::TimestampMicrosecondBuilder>() => {
            let b = b
                .as_any_mut()
                .downcast_mut::<array::TimestampMicrosecondBuilder>()
                .unwrap();
            if let ColumnData::DateTime2(Some(dt)) = col_data {
                let decoded = decode_datetime2(&dt);
                let ts = decoded.and_utc().timestamp_micros();
                b.append_value(ts);
            } else {
                b.append_null();
            }
        }
        b if b.as_any().is::<array::Float64Builder>() => {
            let b = b
                .as_any_mut()
                .downcast_mut::<array::Float64Builder>()
                .unwrap();
            if let ColumnData::F64(Some(v)) = col_data {
                b.append_value(v);
            } else {
                b.append_null();
            }
        }

        _ => {
            eprintln!("Unhandled type: {:?}", dtype);
        }
    }
}

pub fn sql_type_to_arrow(sql_type: &str, char_len: Option<i32>) -> DataType {
    match sql_type.to_lowercase().as_str() {
        // integers
        "tinyint" => DataType::UInt8,
        "smallint" => DataType::Int16,
        "int" => DataType::Int32,
        "bigint" => DataType::Int64,

        // decimals / numerics
        "decimal" | "numeric" => {
            // SQL Server default precision = 18, scale = 0 if not specified
            // Arrow Decimal128 needs precision and scale.
            // If char_len is Some, it often represents precision for numeric/decimal.
            let precision = char_len.unwrap_or(18) as u8;
            let scale = 0; // You could extend to read from INFORMATION_SCHEMA.NUMERIC_SCALE
            DataType::Decimal128(precision, scale)
        }

        // floating point
        "real" => DataType::Float32,
        "float" => DataType::Float64,

        // bit
        "bit" => DataType::Boolean,

        // temporal types
        "date" => DataType::Date32, // days since epoch
        "datetime" | "datetime2" | "smalldatetime" => {
            DataType::Timestamp(TimeUnit::Microsecond, Some(Arc::from("UTC")))
        }
        "time" => DataType::Time64(TimeUnit::Microsecond),

        // character/string types
        "varchar" | "nvarchar" | "char" | "nchar" | "text" | "ntext" => DataType::Utf8,

        // binary / varbinary
        "binary" | "varbinary" | "image" => DataType::Binary,

        // uniqueidentifier (GUID)
        "uniqueidentifier" => DataType::FixedSizeBinary(16),

        // fallback
        other => {
            eprintln!("Warning: unhandled SQL type {}, defaulting to Utf8", other);
            DataType::Utf8
        }
    }
}
