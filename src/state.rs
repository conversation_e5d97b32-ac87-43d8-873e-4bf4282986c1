use crate::db::mssql::connection::connect_to_mssql_with_deadpool;
use crate::db::mssql::connection::pool::Manager;
use deadpool::managed::Object;

pub struct AppState {
    pub pool: Object<Manager>,
}

impl AppState {
    pub async fn new() -> Self {
        Self {
            pool: connect_to_mssql_with_deadpool(30)
                .await
                .get()
                .await
                .unwrap(),
        }
    }
}
