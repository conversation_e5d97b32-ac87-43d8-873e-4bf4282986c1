use crate::db::error::DatabaseError;
use actlogica_logs::{builder::LogBuilder, log_info};
use sqlx::mssql::{Encrypt, MssqlConnectOptions, MssqlPool, MssqlPoolOptions};
pub mod pool;
use pool as deadpool_tiberius;

pub async fn connect_to_mssql(pool_size: u8) -> Result<MssqlPool, DatabaseError> {
    log_info(LogBuilder::system("Connecting to MSSQL DB..."));
    let start = std::time::Instant::now();
    let db_host = std::env::var("DATABASE_HOST").expect("Failed to load database host from env");
    let db_user = std::env::var("DATABASE_USER").expect("Failed to load database user from env");
    let db_password =
        std::env::var("DATABASE_PASSWORD").expect("Failed to load database password from env");
    let db_password = urlencoding::decode(&db_password)
        .expect("Failed to decode database password")
        .to_string();
    let db_port = std::env::var("DATABASE_PORT")
        .expect("Failed to load database port from env")
        .parse::<u16>()
        .expect("Database Port should be an Integer");
    let db_name = std::env::var("DATABASE_NAME").expect("Failed to load database name from env");
    let opts = MssqlConnectOptions::new()
        .host(&db_host)
        .port(db_port)
        .username(&db_user)
        .password(&db_password)
        .database(&db_name)
        .encrypt(Encrypt::NotSupported)
        .trust_server_certificate(true);
    let pool = MssqlPoolOptions::new()
        .acquire_timeout(std::time::Duration::from_secs(30))
        .max_connections(pool_size as u32)
        .connect_with(opts)
        .await
        .map_err(|err| DatabaseError::ConnectionError(err.to_string()))?;

    let end = std::time::Instant::now();
    log_info(LogBuilder::system(&format!(
        "MSSQL connection established, in: {:?}",
        end - start
    )));
    Ok(pool)
}


pub async fn connect_to_mssql_with_deadpool(pool_size: u8) -> deadpool::managed::Pool<pool::Manager> {
    log_info(LogBuilder::system("Connecting to MSSQL DB..."));
    let start = std::time::Instant::now();
    let db_host = std::env::var("DATABASE_HOST").expect("Failed to load database host from env");
    let db_user = std::env::var("DATABASE_USER").expect("Failed to load database user from env");
    let db_password = std::env::var("DATABASE_PASSWORD").expect("Failed to load database password from env");
    let db_port = std::env::var("DATABASE_PORT")
        .expect("Failed to load database port from env")
        .parse()
        .expect("Datbase Port should be an Integer");
    let db_name = std::env::var("DATABASE_NAME").expect("Failed to load database name from env");

    let pool: deadpool::managed::Pool<pool::Manager> = deadpool_tiberius::Manager::new()
        .host(db_host) // default to localhost
        .port(db_port) // default to
        .basic_authentication(db_user, db_password)
        //  or .authentication(tiberius::AuthMethod)
        .database(db_name)
        .max_size(pool_size as usize)
        .pre_recycle_sync(|_client, _metrics| {
            // do sth with client object and pool metrics
            Ok(())
        })
        .wait_timeout(30)
        .create_timeout(30)
        .recycle_timeout(30)
        .post_recycle_sync(|_c, _p| Ok(()))
        .encryption(tiberius::EncryptionLevel::Required)
        .trust_cert()
        .create_pool()
        .unwrap();

    let end = std::time::Instant::now();
    log_info(LogBuilder::system(&format!(
        "MSSQL connection established, in: {:?}",
        end - start
    )));
    pool
}