use crate::db::error::DatabaseError;
use crate::db::mssql::connection::pool::Manager;
use chrono::NaiveDateTime;
use deadpool::managed::Object;
use tiberius::Row;

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ClientWithDetails {
    pub id: String,
    pub client_code: String,
    pub first_name: String,
    pub last_name: String,
    pub date_of_birth: Option<NaiveDateTime>,
    pub pan: String,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub client_type: Option<String>,
    pub domicile: Option<String>,
    pub full_name: Option<String>,
    pub client_status: Option<String>,
    pub address_line_1: Option<String>,
    pub address_line_2: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub country: Option<String>,
    pub pin_code: Option<String>,
    pub email_primary: Option<String>,
    pub mobile_primary: Option<String>,
}

impl ClientWithDetails {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            client_code: row.get::<&str, _>("ClientCode").unwrap().to_string(),
            first_name: row.get::<&str, _>("FirstName").unwrap().to_string(),
            last_name: row.get::<&str, _>("LastName").unwrap().to_string(),
            date_of_birth: row.get::<NaiveDateTime, _>("DateOfBirth").map(|d| d),
            pan: row.get::<&str, _>("Pan").unwrap().to_string(),
            email: row.get::<&str, _>("email").map(String::from),
            phone: row.get::<&str, _>("Phone").map(String::from),
            client_type: row.get::<&str, _>("ClientType").map(String::from),
            domicile: row.get::<&str, _>("Domicile").map(String::from),
            full_name: row.get::<&str, _>("FullName").map(String::from),
            client_status: row.get::<&str, _>("ClientStatus").map(String::from),
            address_line_1: row.get::<&str, _>("AddressLine1").map(String::from),
            address_line_2: row.get::<&str, _>("AddressLine2").map(String::from),
            city: row.get::<&str, _>("City").map(String::from),
            state: row.get::<&str, _>("State").map(String::from),
            country: row.get::<&str, _>("Country").map(String::from),
            pin_code: row.get::<&str, _>("PinCode").map(String::from),
            email_primary: row.get::<&str, _>("EmailPrimary").map(String::from),
            mobile_primary: row.get::<&str, _>("MobilePrimary").map(String::from),
        }
    }

    pub async fn get_all(conn: &mut Object<Manager>) -> Result<Vec<Self>, DatabaseError> {
        let query = "SELECT TOP 100 * FROM vw_query_ClientWithDetails";
        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?;

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();
        Ok(rows)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::db::mssql::connection::connect_to_mssql_with_deadpool;
    use dotenv::dotenv;
    #[tokio::test]
    async fn test_client_with_details() {
        dotenv().ok();
        let mut conn = connect_to_mssql_with_deadpool(30)
            .await
            .get()
            .await
            .unwrap();
        let clients = ClientWithDetails::get_all(&mut conn).await.unwrap();
        println!("{:?}", clients[1]);
        assert!(!clients.is_empty());
    }
}
