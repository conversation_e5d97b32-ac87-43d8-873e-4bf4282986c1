use crate::db::minio::MinIoStruct;
use arrow::json;
use arrow::record_batch::RecordBatch;
use deltalake::datafusion::prelude::{SessionContext, col};
use deltalake::kernel::{DataType as DeltaType, StructField};
use deltalake::writer::{DeltaWriter, RecordBatchWriter};
use deltalake::{DeltaOps, DeltaTable, DeltaTableError};
use std::sync::Arc;

pub struct DeltaLakeStruct {}

impl DeltaLakeStruct {
    pub async fn insert<T: serde::Serialize>(data: Vec<T>, table_path: &str) -> Result<(), ()> {
        // 1. Convert data into JSON lines (one object per line)
        let mut json_lines = Vec::new();
        for d in data {
            let line = serde_json::to_string(&d).unwrap();
            json_lines.push(line);
        }
        let json_str = json_lines.join("\n");

        // 2. Wrap in Cursor
        let mut cursor = std::io::Cursor::new(json_str);

        let inferred_schema = Arc::new(
            json::reader::infer_json_schema(&mut cursor, None)
                .expect("Failed to infer schema")
                .0,
        );

        // 3. Reset cursor for reading again
        cursor.set_position(0);

        // 4. Build RecordBatches from JSON
        let reader = json::ReaderBuilder::new(inferred_schema.clone())
            .build(cursor)
            .unwrap()
            .collect::<arrow::error::Result<Vec<RecordBatch>>>()
            .unwrap();

        // 5. Open Delta Table
        let table = get_or_create_table(table_path, inferred_schema.clone())
            .await
            .unwrap();

        // 6. Write RecordBatches
        let mut writer = RecordBatchWriter::for_table(&table).unwrap();
        for batch in reader {
            writer.write(batch).await.unwrap();
        }
        let commit_info = writer.flush_and_commit(&mut table.clone()).await.unwrap();
        println!("Wrote {} rows", commit_info);
        Ok(())
    }

    pub async fn insert_record_batch(
        data_batches: Vec<RecordBatch>,
        table_path: &str,
    ) -> Result<(), ()> {
        let schema = data_batches[0].schema();

        // Open or create the table
        let mut table = get_or_create_table(table_path, schema.clone())
            .await
            .map_err(|_| ())?;

        let mut writer = deltalake::writer::RecordBatchWriter::for_table(&table).unwrap();
        for batch in data_batches {
            writer.write(batch).await.unwrap();
        }
        writer.flush_and_commit(&mut table).await.unwrap();

        Ok(())
    }

    pub async fn upsert_record_batch(
        data_batches: Vec<RecordBatch>,
        table_path: &str,
        merge_key: &str,
    ) -> Result<(), ()> {
        println!("Merge key provided: '{}'", merge_key);

        let schema = data_batches[0].schema();
        let table = get_or_create_table(table_path, schema.clone())
            .await
            .map_err(|_| ())?;
        
        // Create a temporary table with new data
        let temp_table_path = format!("{}_temp", table_path);
        
        // Delete temp table if it exists before creating new one
        println!("Cleaning up any existing temp table...");
        if let Ok(existing_temp) = deltalake::open_table_with_storage_options(
            &temp_table_path,
            MinIoStruct::get_minio_s3_options(),
        ).await {
            // Delete all files in the temp table
            match DeltaOps(existing_temp).delete().await {
                Ok(_) => println!("Existing temp table data cleared"),
                Err(e) => println!("Warning: Could not clear temp table: {:?}", e),
            }
        }
        
        let mut temp_table = get_or_create_table(&temp_table_path, schema.clone())
            .await
            .map_err(|_| ())?;

        // Write to temp table first
        let mut writer = RecordBatchWriter::for_table(&temp_table).unwrap();
        for batch in data_batches {
            writer.write(batch).await.unwrap();
        }
        writer.flush_and_commit(&mut temp_table).await.unwrap();

        // Read temp table as a DataFrame
        let ctx = SessionContext::new();
        let source_df = ctx.read_table(Arc::new(temp_table.clone())).map_err(|_| ())?;

        // Check if merge key exists in schema
        let merge_key_exists = schema.fields().iter().any(|f| f.name() == merge_key);
        println!("Merge key '{}' exists in schema: {}", merge_key, merge_key_exists);
        
        if !merge_key_exists {
            println!("Available column names:");
            for field in schema.fields() {
                println!("  - '{}'", field.name());
            }
            return Err(());
        }

        // Build predicate with proper quoting for case-sensitive column names
        let quoted_merge_key = format!("\"{}\"", merge_key);
        let on_predicate = col(format!("target.{}", quoted_merge_key))
            .eq(col(format!("source.{}", quoted_merge_key)));

        println!("Merge predicate: target.{} = source.{}", quoted_merge_key, quoted_merge_key);

        // Build column assignments for update/insert
        let assignments: Vec<(String, deltalake::datafusion::logical_expr::Expr)> = schema
            .fields()
            .iter()
            .map(|f| {
                let name = f.name().to_string();
                let source_col = format!("source.\"{}\"", name);
                let expr = col(source_col.clone());
                (name.clone(), expr)
            })
            .collect();

        // Perform MERGE operation
        let merge_builder = DeltaOps(table)
            .merge(source_df, on_predicate)
            .with_source_alias("source")
            .with_target_alias("target")
            .when_matched_update(|update| {
                let mut builder = update;
                for (col_name, expr) in &assignments {
                    builder = builder.update(col_name, expr.clone());
                }
                builder
            })
            .expect("Failed to build merge operation, update failed...")
            .when_not_matched_insert(|insert| {
                let mut builder = insert;
                for (col_name, expr) in &assignments {
                    builder = builder.set(col_name, expr.clone());
                }
                builder
            })
            .expect("Failed to build merge operation, insertion failed...");

        println!("Executing merge operation...");
        merge_builder
            .await
            .expect("Failed to commit merge operation");

        // Clean up temp table after successful merge
        println!("Cleaning up temp table...");
        match DeltaOps(temp_table).vacuum().await {
            Ok(_) => println!("Temp table cleaned up successfully"),
            Err(e) => println!("Warning: Could not clean up temp table: {:?}", e),
        }

        println!("=== UPSERT DEBUG END ===");
        Ok(())
    }
}

async fn get_or_create_table(
    table_path: &str,
    schema: Arc<arrow::datatypes::Schema>,
) -> Result<DeltaTable, ()> {
    // Register AWS handler for custom endpoints
    deltalake::aws::register_handlers(None); // ! <-- IMPORTANT for minio connect using s3 api

    match deltalake::open_table_with_storage_options(
        table_path,
        MinIoStruct::get_minio_s3_options(),
    )
    .await
    {
        Ok(table) => Ok(table),
        Err(DeltaTableError::NotATable(_)) => {
            let delta_fields: Vec<StructField> = schema
                .fields()
                .iter()
                .map(|f| {
                    StructField::new(
                        f.name().clone(),
                        match f.data_type() {
                            arrow::datatypes::DataType::Int64 => DeltaType::INTEGER,
                            arrow::datatypes::DataType::Float64 => DeltaType::DOUBLE,
                            arrow::datatypes::DataType::Utf8 => DeltaType::STRING,
                            arrow::datatypes::DataType::Boolean => DeltaType::BOOLEAN,
                            arrow::datatypes::DataType::Timestamp(_, _) => DeltaType::TIMESTAMP,
                            _ => DeltaType::STRING,
                        },
                        f.is_nullable(),
                    )
                })
                .collect();

            let ops = DeltaOps::try_from_uri_with_storage_options(
                table_path,
                MinIoStruct::get_minio_s3_options(),
            )
            .await
            .unwrap();
            let table = ops.create().with_columns(delta_fields).await.unwrap();
            return Ok(table);
        }
        Err(e) => {
            println!("Failed to load table: {:?}", e);
            Err(())
        } // real error
    }
}
