use std::collections::HashMap;

pub struct MinIoStruct {}

impl MinIoStruct {
    pub fn get_minio_s3_options() -> HashMap<String, String> {
        dotenv::dotenv().ok();
        let mut options = HashMap::new();
        options.insert(
            "AWS_ACCESS_KEY_ID".into(),
            std::env::var("MINIO_ACCESS_KEY").expect("MINIO_ACCESS_KEY not found"),
        );
        options.insert(
            "AWS_SECRET_ACCESS_KEY".into(),
            std::env::var("MINIO_SECRET_KEY").unwrap(),
        );
        options.insert(
            "AWS_ENDPOINT_URL".into(),
            std::env::var("MINIO_ENDPOINT").expect("MINIO_ENDPOINT not found"),
        );
        options.insert(
            "AWS_ALLOW_HTTP".into(),
            std::env::var("AWS_ALLOW_HTTP").unwrap_or_else(|_| "true".to_string()),
        );
        options
    }
}
