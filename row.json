Metadata(ResultMetadata { columns: [<PERSON>umn { name: "Id", column_type: <PERSON>VarChar
        }, Column { name: "ClientCode", column_type: BigVarChar
        }, Column { name: "<PERSON>Name", column_type: <PERSON>Var<PERSON>har
        }, Column { name: "LastN<PERSON>", column_type: BigVarChar
        }, Column { name: "DateOfBirth", column_type: Datetime2
        }, Column { name: "<PERSON>", column_type: BigVarChar
        }, Column { name: "email", column_type: BigVarChar
        }, Column { name: "Phone", column_type: BigVarChar
        }, Column { name: "ClientT<PERSON>", column_type: NVarchar
        }, Column { name: "<PERSON><PERSON><PERSON>", column_type: NVarchar
        }, Column { name: "FullName", column_type: BigVarChar
        }, Column { name: "ClientStatus", column_type: BigVarChar
        }, Column { name: "AddressLine1", column_type: BigVarChar
        }, Column { name: "AddressLine2", column_type: <PERSON>Var<PERSON>har
        }, Column { name: "City", column_type: BigVarChar
        }, Column { name: "State", column_type: BigVarChar
        }, Column { name: "Country", column_type: BigVarChar
        }, Column { name: "PinCode", column_type: BigVarChar
        }, Column { name: "EmailPrimary", column_type: BigVarChar
        }, Column { name: "MobilePrimary", column_type: BigVarChar
        }
    ], result_index: 0
})
Row(Row { columns: [Column { name: "Id", column_type: BigVarChar
        }, Column { name: "ClientCode", column_type: BigVarChar
        }, Column { name: "FirstName", column_type: BigVarChar
        }, Column { name: "LastName", column_type: BigVarChar
        }, Column { name: "DateOfBirth", column_type: Datetime2
        }, Column { name: "Pan", column_type: BigVarChar
        }, Column { name: "email", column_type: BigVarChar
        }, Column { name: "Phone", column_type: BigVarChar
        }, Column { name: "ClientType", column_type: NVarchar
        }, Column { name: "Domicile", column_type: NVarchar
        }, Column { name: "FullName", column_type: BigVarChar
        }, Column { name: "ClientStatus", column_type: BigVarChar
        }, Column { name: "AddressLine1", column_type: BigVarChar
        }, Column { name: "AddressLine2", column_type: BigVarChar
        }, Column { name: "City", column_type: BigVarChar
        }, Column { name: "State", column_type: BigVarChar
        }, Column { name: "Country", column_type: BigVarChar
        }, Column { name: "PinCode", column_type: BigVarChar
        }, Column { name: "EmailPrimary", column_type: BigVarChar
        }, Column { name: "MobilePrimary", column_type: BigVarChar
        }
    ], data: TokenRow { data: [String(Some("0085332155094708ac77946dde1f092c")), String(Some("DevClient-92")), String(Some("DevClient -")), String(Some("92")), DateTime2(Some(DateTime2 { date: Date(734486), time: Time { increments: 0, scale: 7
                }
            })), String(Some("DNFPA3300")), String(Some("<EMAIL>")), String(Some("9876564345")), String(Some("Individual")), String(Some("Resident")), String(None), String(Some("Active")), String(Some("Line 83")), String(None), String(Some("Mumbai")), String(Some("Maharashtra")), String(Some("India")), String(Some("878869")), String(None), String(None)
        ]
    }, result_index: 0
})