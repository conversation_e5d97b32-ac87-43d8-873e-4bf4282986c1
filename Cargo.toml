[package]
name = "alpha-report-builder"
version = "0.1.0"
edition = "2024"

[dependencies]
# Async Runtime and Utilities
tokio = { version = "1", features = ["full"] }
tokio-util = { version = "0.7.11", features = ["compat"] }
futures = "0.3.30"
futures-util = "0.3.30"

# Database Drivers and Connections
sqlx = { package = "sqlx-oldapi", version = "0.6", features = ["runtime-tokio-native-tls", "mssql"] }
clickhouse = { version = "0.13.3", features = ["native-tls", "time", "chrono"] }
deltalake = { version = "0.27", features = ["json", "datafusion", "s3"] }
tiberius = { version = "0.12.3", features = [
    "vendored-openssl",
    "tds73",
    "time",
    "rust_decimal",
    "chrono",
], default-features = false }
deadpool = { version = "0.12.1", features = ["rt_tokio_1"] }

# Serialization and Data Formats
serde = { version = "1.0.204", features = ["derive"] }
serde_json = "1.0.122"
arrow ={ version = "55.1.0", features = ["json"] }

# Time and Date
chrono = { version = "0.4.40", features = ["serde"] }

# Security and Encryption
native-tls = "0.2.12"

# Utilities
dotenv = "0.15.0"
uuid = "1.10.0"
urlencoding = "2.1"
thiserror = "1.0.64"

# Logging and Tracing
tracing = { version = "0.1.40", features = ["attributes"] }
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }

# API Documentation
utoipa = { version = "5.3.1", features = ["axum_extras"] }
utoipa-swagger-ui = { version = "9.0.1", features = ["axum"] }

# Alpha-Logger
actlogica_logs = { git = "ssh://*********************/v3/actlogica/actlogica-logger/actlogica-logger", branch = "main" }
anyhow = "1.0.99"
base64 = "0.22.1"
